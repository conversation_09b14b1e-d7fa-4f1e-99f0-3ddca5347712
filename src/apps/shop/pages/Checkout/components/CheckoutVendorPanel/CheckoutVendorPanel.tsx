import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { Divider, Image, Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import defaultProductImgUrl from '@/assets/images/default-product.png';
import { getPriceString } from '@/utils';
import { usePromotionData } from '@/libs/promotions/hooks/usePromotionData';
import { CheckoutPromoItem } from '../CheckoutPromoItem/CheckoutPromoItem';
import { CartVendorType } from '@/libs/cart/types';
import styles from '../../Checkout.module.css';

type CheckoutVendorPanelProps = {
  vendor: CartVendorType;
};

export const CheckoutVendorPanel = ({ vendor }: CheckoutVendorPanelProps) => {
  const { id, name, imageUrl, items } = vendor;
  const { promotions: promos, nonPromotionItems: noPromoItems } =
    usePromotionData(items);

  return (
    <div key={id} className="mb-4">
      <CollapsiblePanel
        header={
          <Flex align="center" pr="5rem">
            <Image
              src={imageUrl}
              alt={name}
              fallbackSrc={defaultProductImgUrl}
              h={42}
              title={name}
            />
          </Flex>
        }
        content={
          <div className="p-4">
            {/* Promotion Items */}
            {promos.buy_x_get_y && (
              <div className="mb-4">
                <CheckoutPromoItem promoItem={promos.buy_x_get_y} />
              </div>
            )}
            
            {/* Regular Items */}
            {noPromoItems.length > 0 && (
              <table className={styles.table}>
                <tbody>
                  {noPromoItems.map(
                    ({
                      quantity,
                      product,
                      subtotal,
                      price,
                      productOfferId,
                    }) => (
                      <tr key={productOfferId}>
                        <td width="100%">
                          <Text
                            miw="50%"
                            fw="500"
                            className={styles.productName}
                          >
                            {product?.name}
                          </Text>
                        </td>
                        <td>
                          <Text c="#666">
                            Quantity:{' '}
                            <Text span c="#333" fw="500">
                              {quantity}
                            </Text>
                          </Text>
                          <Divider orientation="vertical" mx="sm" />
                        </td>
                        <td>
                          <Divider
                            orientation="vertical"
                            mx="0.5rem"
                            h="1rem"
                          />
                        </td>
                        <td>
                          <Text c="#666">
                            Price:{' '}
                            <Text span c="#333" fw="500">
                              {getPriceString(price)}
                            </Text>
                          </Text>
                        </td>
                        <td>
                          <Divider
                            orientation="vertical"
                            mx="0.5rem"
                            h="1rem"
                          />
                        </td>
                        <td>
                          <Text c="#666">
                            Net total:{' '}
                            <Text span c="#333" fw="500">
                              {getPriceString(subtotal)}
                            </Text>
                          </Text>
                        </td>
                      </tr>
                    ),
                  )}
                </tbody>
              </table>
            )}
          </div>
        }
        startOpen
      />
    </div>
  );
};
