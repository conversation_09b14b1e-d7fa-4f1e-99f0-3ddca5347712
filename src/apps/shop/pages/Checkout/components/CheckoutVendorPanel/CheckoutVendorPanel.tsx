import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { Divider, Image } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import defaultProductImgUrl from '@/assets/images/default-product.png';
import { getPriceString } from '@/utils';
import { usePromotionData } from '@/libs/promotions/hooks/usePromotionData';
import { CheckoutPromoItem } from '../CheckoutPromoItem/CheckoutPromoItem';
import { CartVendorType } from '@/libs/cart/types';

type CheckoutVendorPanelProps = {
  vendor: CartVendorType;
};

export const CheckoutVendorPanel = ({ vendor }: CheckoutVendorPanelProps) => {
  const { id, name, imageUrl, items } = vendor;
  const { promotions: promos, nonPromotionItems: noPromoItems } =
    usePromotionData(items);

  return (
    <div key={id} className="mb-4">
      <CollapsiblePanel
        header={
          <Flex align="center" pr="5rem">
            <Image
              src={imageUrl}
              alt={name}
              fallbackSrc={defaultProductImgUrl}
              h={42}
              title={name}
            />
          </Flex>
        }
        content={
          <div className="p-4">
            {promos.buy_x_get_y && (
              <div className="mb-4">
                <CheckoutPromoItem promoItem={promos.buy_x_get_y} />
              </div>
            )}

            {noPromoItems.length > 0 && (
              <table className="mb-1 w-full min-w-0 border-collapse text-sm">
                <tbody>
                  {noPromoItems.map(
                    ({
                      quantity,
                      product,
                      subtotal,
                      price,
                      productOfferId,
                    }) => (
                      <tr key={productOfferId}>
                        <td
                          width="100%"
                          className="whitespace-wrap border-none pb-3 align-top"
                        >
                          <span className="max-w-[400px] min-w-[50%] pr-3 font-medium whitespace-normal">
                            {product?.name}
                          </span>
                        </td>
                        <td className="border-none pb-3 align-top whitespace-nowrap">
                          <span className="text-gray-500">
                            Quantity:{' '}
                            <span className="font-semibold text-gray-900">
                              {quantity}
                            </span>
                          </span>
                        </td>
                        <td className="border-none pb-3 align-top whitespace-nowrap">
                          <span className="text-gray-500">
                            Unit:{' '}
                            <span className="font-semibold text-gray-900">
                              {getPriceString(price)}
                            </span>
                          </span>
                        </td>
                        <td className="border-none pb-3 align-top whitespace-nowrap">
                          <span className="text-gray-500">
                            Net total:{' '}
                            <span className="font-semibold text-gray-900">
                              {getPriceString(subtotal)}
                            </span>
                          </span>
                        </td>
                      </tr>
                    ),
                  )}
                </tbody>
              </table>
            )}
          </div>
        }
        startOpen
      />
    </div>
  );
};
