import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { Image } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import defaultProductImgUrl from '@/assets/images/default-product.png';
import { getPriceString } from '@/utils';
import { usePromotionData } from '@/libs/promotions/hooks/usePromotionData';
import { CheckoutPromoItem } from '../CheckoutPromoItem/CheckoutPromoItem';
import { CartVendorType } from '@/libs/cart/types';

type CheckoutVendorPanelProps = {
  vendor: CartVendorType;
};

export const CheckoutVendorPanel = ({ vendor }: CheckoutVendorPanelProps) => {
  const { id, name, imageUrl, items } = vendor;
  const { promotions: promos, nonPromotionItems: noPromoItems } =
    usePromotionData(items);

  return (
    <div key={id} className="mb-4">
      <CollapsiblePanel
        header={
          <Flex align="center" pr="5rem">
            <Image
              src={imageUrl}
              alt={name}
              fallbackSrc={defaultProductImgUrl}
              h={42}
              title={name}
            />
          </Flex>
        }
        content={
          <div className="p-4">
            {promos.buy_x_get_y && (
              <div className="mb-4">
                <CheckoutPromoItem promoItem={promos.buy_x_get_y} />
              </div>
            )}

            {noPromoItems.length > 0 && (
              <div className="space-y-3">
                {noPromoItems.map(
                  ({ quantity, product, subtotal, price, productOfferId }) => (
                    <div
                      key={productOfferId}
                      className="grid gap-2 border-b border-black/5 pb-3 last:border-b-0 md:grid-cols-[1fr_auto_auto_auto] md:items-center md:gap-4"
                    >
                      {/* Product Name */}
                      <div className="min-w-0">
                        <span className="font-medium break-words text-gray-900">
                          {product?.name}
                        </span>
                      </div>

                      {/* Mobile: Details Row */}
                      <div className="flex flex-wrap items-center gap-2 md:hidden">
                        <span className="text-xs text-gray-500">
                          Quantity:{' '}
                          <span className="font-medium text-gray-900">
                            {quantity}
                          </span>
                        </span>
                        <div className="divider-v"></div>
                        <span className="text-xs text-gray-500">
                          Price:{' '}
                          <span className="font-medium text-gray-900">
                            {getPriceString(price)}
                          </span>
                        </span>
                        <div className="divider-v"></div>
                        <span className="text-xs text-gray-500">
                          Net Total:{' '}
                          <span className="font-medium text-gray-900">
                            {getPriceString(subtotal)}
                          </span>
                        </span>
                      </div>

                      {/* Desktop: Quantity */}
                      <div className="hidden items-center whitespace-nowrap md:flex">
                        <span className="text-xs text-gray-500">
                          Quantity:{' '}
                          <span className="font-medium text-gray-900">
                            {quantity}
                          </span>
                        </span>
                        <div className="divider-v"></div>
                      </div>

                      {/* Desktop: Price */}
                      <div className="hidden items-center whitespace-nowrap md:flex">
                        <span className="text-xs text-gray-500">
                          Price:{' '}
                          <span className="font-medium text-gray-900">
                            {getPriceString(price)}
                          </span>
                        </span>
                        <div className="divider-v"></div>
                      </div>

                      {/* Desktop: Net Total */}
                      <div className="hidden whitespace-nowrap md:block">
                        <span className="text-xs text-gray-500">
                          Net Total:{' '}
                          <span className="font-medium text-gray-900">
                            {getPriceString(subtotal)}
                          </span>
                        </span>
                      </div>
                    </div>
                  ),
                )}
              </div>
            )}
          </div>
        }
        startOpen
      />
    </div>
  );
};
