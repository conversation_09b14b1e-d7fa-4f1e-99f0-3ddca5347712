.promoContainer {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background-color: #ffffff;
  margin-bottom: 1rem;
}

.promoHeaderRow {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #f3f4f6;
  gap: 1rem;
}

.promoNameSection {
  flex: 1;
  min-width: 0;
}

.promoLabel {
  margin-bottom: 0.25rem;
}

.promoName {
  line-height: 1.25;
  word-wrap: break-word;
}

.promoQuantity,
.promoPrice,
.promoTotal {
  white-space: nowrap;
}

.promoItemsContainer {
  background-color: #e0f2fe; /* Light blue background */
  padding: 1rem;
}

.promoItemGroup {
  margin-bottom: 1rem;
}

.promoItemGroup:last-of-type {
  margin-bottom: 0;
}

.promoItemRow {
  display: grid;
  grid-template-columns: 2rem 1fr auto;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0;
}

.freeItemRow {
  display: grid;
  grid-template-columns: 2rem 1fr auto;
  align-items: center;
  gap: 0.75rem;
  padding: 0.25rem 0;
  margin-top: 0.25rem;
}

.itemQuantity {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.75rem;
  height: 1.25rem;
  background-color: rgba(182, 245, 249, 0.35);
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: #333;
}

.itemName {
  flex: 1;
  color: #333;
}

.promoSummary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  margin-top: 1rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .promoHeaderRow {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .promoQuantity,
  .promoPrice,
  .promoTotal {
    align-self: flex-start;
  }

  .promoSummary {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
