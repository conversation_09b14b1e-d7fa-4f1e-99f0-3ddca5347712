import { Text, Divider } from '@mantine/core';
import { getPriceString } from '@/utils';
import { BuyXGetYPromotionData } from '@/libs/promotions/types';
import { PROMO_TYPE } from '@/constants';
import styles from './CheckoutPromoItem.module.css';

type CheckoutPromoItemProps = {
  promoItem: BuyXGetYPromotionData;
};

export const CheckoutPromoItem = ({ promoItem }: CheckoutPromoItemProps) => {
  if (!promoItem || !promoItem.promotion || !promoItem.freeOffer) return null;

  // Calculate unit price for the promotion (price per paid item)
  const unitPrice =
    promoItem.paidItemsQty > 0
      ? Number(promoItem.subtotalPaidItems) / promoItem.paidItemsQty
      : Number(promoItem.freeOffer.clinicPrice || promoItem.freeOffer.price);

  return (
    <div className={styles.promoContainer}>
      {/* Promotion Header - Similar to checkout table row */}
      <div className={styles.promoHeaderRow}>
        <div className={styles.promoNameSection}>
          <div className={styles.promoLabel}>
            <Text size="xs" fw="500" c="#22C55E">
              Promotion • {PROMO_TYPE[promoItem.promotion.type]}
            </Text>
          </div>
          <Text size="sm" fw="500" c="#333" className={styles.promoName}>
            {promoItem.promotion.name}
          </Text>
        </div>
        <div className={styles.promoQuantity}>
          <Text c="#666" size="xs">
            Quantity:{' '}
            <Text span c="#333" fw="500">
              {promoItem.paidItemsQty}
            </Text>
          </Text>
        </div>
        <Divider orientation="vertical" mx="sm" h="1rem" />
        <div className={styles.promoPrice}>
          <Text c="#666" size="xs">
            Price:{' '}
            <Text span c="#333" fw="500">
              {getPriceString(unitPrice)}
            </Text>
          </Text>
        </div>
        <Divider orientation="vertical" mx="sm" h="1rem" />
        <div className={styles.promoTotal}>
          <Text c="#666" size="xs">
            Net Total:{' '}
            <Text span c="#333" fw="500">
              {getPriceString(promoItem.subtotalPaidItems)}
            </Text>
          </Text>
        </div>
      </div>

      {/* Promotion Items Details - Light blue section */}
      <div className={styles.promoItemsContainer}>
        {promoItem.items.map((item) => (
          <div key={item.id} className={styles.promoItemGroup}>
            {/* Paid item */}
            <div className={styles.promoItemRow}>
              <div className={styles.itemQuantity}>{item.quantity}</div>
              <Text size="xs" fw="500" className={styles.itemName}>
                {item.product.name}
              </Text>
              <Text size="xs" c="#333" fw="500">
                {getPriceString(item.subtotal)}
              </Text>
            </div>
            {/* Free item */}
            {item.freeItemsQty > 0 && item.freeOffer && (
              <div className={styles.freeItemRow}>
                <div className={styles.itemQuantity}>{item.freeItemsQty}</div>
                <Text size="xs" fw="500" className={styles.itemName}>
                  {item.freeOffer.name}
                </Text>
                <Text size="xs" c="#22C55E" fw="500">
                  Free
                </Text>
              </div>
            )}
          </div>
        ))}

        {/* Promotion Summary at bottom of light blue section */}
        <div className={styles.promoSummary}>
          <Text size="xs" c="#666">
            You're getting total of{' '}
            <Text span fw="500" c="#333">
              {promoItem.paidItemsQty + promoItem.freeItemsQty}
            </Text>{' '}
            products
          </Text>
          <Text size="xs" c="#666">
            Promotional Savings:{' '}
            <Text span fw="500" c="#333">
              {getPriceString(
                promoItem.subtotalAllItems - promoItem.subtotalPaidItems,
              )}
            </Text>
          </Text>
        </div>
      </div>
    </div>
  );
};
