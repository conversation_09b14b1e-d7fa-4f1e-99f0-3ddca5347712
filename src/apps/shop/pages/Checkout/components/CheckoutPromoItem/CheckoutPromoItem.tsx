import { Divider } from '@mantine/core';
import { getPriceString } from '@/utils';
import { BuyXGetYPromotionData } from '@/libs/promotions/types';
import { PROMO_TYPE } from '@/constants';

type CheckoutPromoItemProps = {
  promoItem: BuyXGetYPromotionData;
};

export const CheckoutPromoItem = ({ promoItem }: CheckoutPromoItemProps) => {
  if (!promoItem || !promoItem.promotion || !promoItem.freeOffer) return null;

  // Calculate unit price for the promotion (price per paid item)
  const unitPrice =
    promoItem.paidItemsQty > 0
      ? Number(promoItem.subtotalPaidItems) / promoItem.paidItemsQty
      : Number(promoItem.freeOffer.clinicPrice || promoItem.freeOffer.price);

  return (
    <div className="mb-4 rounded-lg border border-gray-200 bg-white">
      {/* Promotion Header - Similar to checkout table row */}
      <div className="flex flex-col gap-4 border-b border-gray-100 p-4 md:flex-row md:items-center">
        <div className="min-w-0 flex-1">
          <div className="mb-1">
            <span className="text-xs font-medium text-green-600">
              Promotion • {PROMO_TYPE[promoItem.promotion.type]}
            </span>
          </div>
          <h3 className="text-sm leading-tight font-medium break-words text-gray-900">
            {promoItem.promotion.name}
          </h3>
        </div>
        <div className="whitespace-nowrap">
          <span className="text-xs text-gray-500">
            Quantity:{' '}
            <span className="font-medium text-gray-900">
              {promoItem.paidItemsQty}
            </span>
          </span>
        </div>
        <Divider orientation="vertical" mx="sm" h="1rem" />
        <div className="whitespace-nowrap">
          <span className="text-xs text-gray-500">
            Price:{' '}
            <span className="font-medium text-gray-900">
              {getPriceString(unitPrice)}
            </span>
          </span>
        </div>
        <Divider orientation="vertical" mx="sm" h="1rem" />
        <div className="whitespace-nowrap">
          <span className="text-xs text-gray-500">
            Net Total:{' '}
            <span className="font-medium text-gray-900">
              {getPriceString(promoItem.subtotalPaidItems)}
            </span>
          </span>
        </div>
      </div>

      {/* Promotion Items Details - Light blue section */}
      <div className="bg-[#B6F5F926] p-4">
        {promoItem.items.map((item) => (
          <div key={item.id} className="mb-4 last:mb-0">
            {/* Paid item */}
            <div className="grid grid-cols-[2rem_1fr_auto] items-center gap-3 py-2">
              <div className="flex h-5 w-7 items-center justify-center rounded bg-cyan-100 text-xs font-medium text-gray-900">
                {item.quantity}
              </div>
              <span className="flex-1 text-xs font-medium text-gray-900">
                {item.product.name}
              </span>
              <span className="text-xs font-medium text-gray-900">
                {getPriceString(item.subtotal)}
              </span>
            </div>
            {/* Free item */}
            {item.freeItemsQty > 0 && item.freeOffer && (
              <div className="mt-1 grid grid-cols-[2rem_1fr_auto] items-center gap-3 py-1">
                <div className="flex h-5 w-7 items-center justify-center rounded bg-cyan-100 text-xs font-medium text-gray-900">
                  {item.freeItemsQty}
                </div>
                <span className="flex-1 text-xs font-medium text-gray-900">
                  {item.freeOffer.name}
                </span>
                <span className="text-xs font-medium text-green-600">Free</span>
              </div>
            )}
          </div>
        ))}

        {/* Promotion Summary at bottom of light blue section */}
        <div className="mt-4 flex flex-col gap-2 border-t border-black/10 pt-4 md:flex-row md:items-center md:justify-between">
          <span className="text-xs text-gray-500">
            You&apos;re getting total of{' '}
            <span className="font-medium text-gray-900">
              {promoItem.paidItemsQty + promoItem.freeItemsQty}
            </span>{' '}
            products
          </span>
          <span className="text-xs text-gray-500">
            Promotional Savings:{' '}
            <span className="font-medium text-gray-900">
              {getPriceString(
                promoItem.subtotalAllItems - promoItem.subtotalPaidItems,
              )}
            </span>
          </span>
        </div>
      </div>
    </div>
  );
};
