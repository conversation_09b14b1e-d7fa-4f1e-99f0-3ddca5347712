import { getPriceString } from '@/utils';
import { OfferType } from '@/types';
import { Icon } from '@/libs/icons/Icon';
import { Tooltip } from '@mantine/core';
import { Button } from '@/libs/ui/Button/Button';

type CartVendorProductItemActionsProps = {
  quantity: number;
  offer: OfferType;
  subtotal: number;
  subtotalWithoutDiscount: number;
  onRemove?: () => void;
};

export const CartVendorPromoItemActions = ({
  quantity,
  offer,
  subtotal,
  subtotalWithoutDiscount,
  onRemove,
}: CartVendorProductItemActionsProps) => (
  <div className="grid grid-cols-1">
    {quantity && (
      <>
        <div className="mb-1 flex items-end justify-end">
          <span className="text-xs font-medium text-neutral-500">
            Unit Price:{' '}
            <span className="text-xs font-medium text-black">
              {getPriceString(offer.clinicPrice || offer.price)}
            </span>
          </span>
          <Icon name="multiply" />
        </div>
        <div className="grid h-10 w-36 place-items-center rounded-sm border border-black/10">
          {quantity}
        </div>
      </>
    )}

    <div className="mb-1 flex flex-row-reverse flex-wrap items-baseline">
      <span className="my-5 ml-1 text-xl leading-0 font-medium">
        {getPriceString(subtotal)}
      </span>
      {subtotal && subtotalWithoutDiscount > +subtotal && (
        <span className="text-sm leading-0 text-neutral-400 line-through">
          {getPriceString(subtotalWithoutDiscount)}
        </span>
      )}
    </div>

    {onRemove && (
      <div className="flex justify-end">
        <Tooltip label="Remove promotion from cart">
          <Button
            onClick={onRemove}
            variant="unstyled"
            aria-label="Remove promotion from cart"
          >
            <Icon name="trash" color="#667085" size="1rem" aria-hidden={true} />
          </Button>
        </Tooltip>
      </div>
    )}
  </div>
);
